#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试多关键词功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from web_spider import CninfoSpider

def test_multi_keyword_analysis():
    """测试多关键词分析功能"""
    print("=== 测试多关键词分析功能 ===")
    
    # 创建爬虫实例
    spider = CninfoSpider()
    
    # 测试文本
    test_text = """
    公司简介：本公司是一家专注于技术创新的企业。
    
    第一章 协同创新发展
    公司与多家高校开展协同创新项目，通过产学研合作实现技术突破。
    我们建立了完善的技术合作体系，与合作伙伴共同推进创新发展。
    
    第二章 技术合作模式
    公司采用开放式的技术合作模式，与行业内的领先企业建立战略合作关系。
    通过技术交流和合作研发，实现了多项技术创新。
    
    第三章 产学研结合
    公司积极推进产学研一体化发展，与知名大学建立联合实验室。
    这种产学研结合的模式为公司带来了持续的创新动力。
    """
    
    # 测试关键词列表
    test_keywords = [
        "协同创新",      # 单关键词
        "协同&创新",     # 多关键词组合
        "技术&合作",     # 多关键词组合
        "产学&研",       # 多关键词组合
        "技术&交流",     # 多关键词组合
        "不存在&关键词", # 不存在的组合
        "创新&发展",     # 多关键词组合
    ]
    
    print(f"📝 测试文本长度: {len(test_text)} 字符")
    print(f"🔤 测试关键词: {test_keywords}")
    
    # 执行关键词分析
    results = spider.analyze_keywords(test_text, test_keywords)
    
    print(f"\n📊 分析结果:")
    for keyword, count in results.items():
        status = "✅" if count > 0 else "❌"
        print(f"{status} '{keyword}': {count} 次")
    
    return results

def test_innovation_context_check():
    """测试创新上下文检查功能"""
    print("\n=== 测试创新上下文检查功能 ===")
    
    spider = CninfoSpider()
    
    # 测试上下文
    test_contexts = [
        "公司与高校开展协同创新项目，通过技术合作实现发展。",
        "企业建立了完善的技术合作体系，推进创新发展。",
        "公司采用开放式的技术合作模式，与企业建立合作关系。",
        "产学研结合的模式为公司带来了创新动力。",
        "这是一段不包含任何相关关键词的文本内容。",
    ]
    
    test_keywords = [
        "协同创新",
        "协同&创新", 
        "技术&合作",
        "产学&研",
        "创新&发展",
        "不存在&关键词",
    ]
    
    for i, context in enumerate(test_contexts):
        print(f"\n📝 测试上下文 {i+1}: {context[:50]}...")
        
        result = spider.check_innovation_in_context(context, test_keywords)
        
        print(f"  🔍 是否包含创新关键词: {result['has_innovation']}")
        if result['keywords_found']:
            print(f"  ✅ 找到的关键词: {result['keywords_found']}")
        else:
            print(f"  ❌ 未找到任何关键词")

def test_single_keyword_check():
    """测试单关键词检查功能"""
    print("\n=== 测试单关键词检查功能 ===")
    
    spider = CninfoSpider()
    
    context = "公司与高校开展协同创新项目，通过技术合作实现发展。"
    
    test_cases = [
        ("协同", True),
        ("创新", True),
        ("技术", True),
        ("合作", True),
        ("发展", True),
        ("研发", False),
        ("不存在", False),
    ]
    
    for keyword, expected in test_cases:
        result = spider._check_single_keyword_in_context(context, keyword)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{keyword}' 在上下文中: {result} (期望: {expected})")

if __name__ == "__main__":
    try:
        # 运行测试
        test_multi_keyword_analysis()
        test_innovation_context_check()
        test_single_keyword_check()
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
