#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试上下文提取功能，特别是多关键词支持
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import (
    extract_keyword_context, 
    find_keyword_contexts, 
    extract_keywords_from_context,
    check_single_keyword_in_context,
    parse_multi_keywords,
    check_multi_keyword_match
)

def test_extract_keywords_from_context():
    """测试从上下文中提取关键词"""
    print("=== 测试从上下文中提取关键词 ===")
    
    context = "公司与高校开展协同创新项目，通过技术合作实现共同发展。产学研结合是重要的创新模式。"
    
    test_cases = [
        (["协同创新"], ["协同创新"]),
        (["协同&创新"], ["协同&创新"]),
        (["技术&合作"], ["技术&合作"]),
        (["产学&研"], ["产学&研"]),
        (["协同&发展"], ["协同&发展"]),
        (["技术&研发"], []),  # 研发不在文本中
        (["协同创新", "技术&合作"], ["协同创新", "技术&合作"]),
        (["不存在", "技术&合作"], ["技术&合作"]),
    ]
    
    for keywords, expected in test_cases:
        result = extract_keywords_from_context(context, keywords)
        status = "✅" if set(result) == set(expected) else "❌"
        print(f"{status} 关键词 {keywords} -> {result} (期望: {expected})")

def test_check_single_keyword_in_context():
    """测试单个关键词在上下文中的检查"""
    print("\n=== 测试单个关键词检查 ===")
    
    context = "公司与高校开展协同创新项目，通过技术合作实现共同发展。"
    
    test_cases = [
        ("协同", True),
        ("创新", True),
        ("技术", True),
        ("合作", True),
        ("研发", False),
        ("不存在", False),
    ]
    
    for keyword, expected in test_cases:
        result = check_single_keyword_in_context(context, keyword)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{keyword}' 在上下文中: {result} (期望: {expected})")

def test_find_keyword_contexts():
    """测试关键词上下文查找"""
    print("\n=== 测试关键词上下文查找 ===")
    
    text = """
    公司简介：本公司是一家专注于技术创新的企业。
    
    第一章 协同创新发展
    公司与多家高校开展协同创新项目，通过产学研合作实现技术突破。
    我们建立了完善的技术合作体系，与合作伙伴共同推进创新发展。
    
    第二章 技术合作模式
    公司采用开放式的技术合作模式，与行业内的领先企业建立战略合作关系。
    通过技术交流和合作研发，实现了多项技术创新。
    
    第三章 产学研结合
    公司积极推进产学研一体化发展，与知名大学建立联合实验室。
    这种产学研结合的模式为公司带来了持续的创新动力。
    """
    
    test_cases = [
        ["协同创新"],
        ["协同&创新"], 
        ["技术&合作"],
        ["产学&研"],
        ["技术&交流"],
        ["不存在&关键词"],  # 应该找不到
    ]
    
    for keywords in test_cases:
        print(f"\n🔍 测试关键词: {keywords}")
        contexts = find_keyword_contexts(text, keywords, context_length=100)
        print(f"📝 找到 {len(contexts)} 个上下文")
        
        for i, ctx in enumerate(contexts):
            print(f"  上下文 {i+1}: 长度={len(ctx['text'])}, 关键词={ctx['keywords_found']}")
            print(f"    内容预览: {ctx['text'][:80]}...")

def test_extract_keyword_context():
    """测试关键词上下文提取"""
    print("\n=== 测试关键词上下文提取 ===")
    
    text = """
    公司简介：本公司是一家专注于技术创新的企业。
    
    第一章 协同创新发展
    公司与多家高校开展协同创新项目，通过产学研合作实现技术突破。
    我们建立了完善的技术合作体系，与合作伙伴共同推进创新发展。
    
    第二章 技术合作模式  
    公司采用开放式的技术合作模式，与行业内的领先企业建立战略合作关系。
    通过技术交流和合作研发，实现了多项技术创新。
    """
    
    test_cases = [
        "协同创新",
        "协同&创新",
        "技术&合作", 
        "产学&研",
    ]
    
    for keyword in test_cases:
        print(f"\n🔍 测试关键词: {keyword}")
        snippets = extract_keyword_context(text, keyword, context_length=100)
        print(f"📝 提取到 {len(snippets)} 个片段")
        
        for i, snippet in enumerate(snippets):
            print(f"  片段 {i+1}: {snippet[:100]}...")

if __name__ == "__main__":
    test_extract_keywords_from_context()
    test_check_single_keyword_in_context()
    test_find_keyword_contexts()
    test_extract_keyword_context()
    print("\n=== 测试完成 ===")
