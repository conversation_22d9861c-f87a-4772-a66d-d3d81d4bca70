"""
网页版爬虫模块 - 整合原有爬虫功能
"""
import requests
import random
import time
import os
import re
import pdfplumber
from typing import List, Dict, Optional, Tuple
from database import DatabaseManager


class WebSpider:
    def __init__(self, db_manager: DatabaseManager):
        """初始化网页爬虫"""
        self.db = db_manager
        self.is_running = False
        
        # 用户代理列表
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ]
        
        # 请求头
        self.headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Host': 'www.cninfo.com.cn',
            'Origin': 'http://www.cninfo.com.cn',
            'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # API URLs
        self.orgid_url = 'http://www.cninfo.com.cn/new/data/szse_stock.json'
        self.query_url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
        self.download_base_url = 'http://static.cninfo.com.cn/'
    
    def get_orgid_by_code(self, stock_code: str) -> Optional[Dict]:
        """根据股票代码获取orgId"""
        try:
            response = requests.get(self.orgid_url, headers=self.headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                stock_lists = data.get('stockList', [])
                for stock_info in stock_lists:
                    if stock_info.get('code') == stock_code:
                        return {
                            'code': stock_info['code'],
                            'orgId': stock_info['orgId'],
                            'zwjc': stock_info.get('zwjc', ''),
                        }
            return None
        except Exception as e:
            print(f"获取orgId失败: {e}")
            return None
    
    def search_announcements(self, stock_code: str, org_id: str, 
                           search_keyword: str = "年度报告", 
                           start_date: str = "2024-01-01", 
                           end_date: str = "2025-12-31") -> List[Dict]:
        """搜索公告"""
        try:
            self.headers['User-Agent'] = random.choice(self.user_agents)
            
            # 深市查询
            szse_query = {
                'pageNum': 1,
                'pageSize': 30,
                'tabName': 'fulltext',
                'column': 'szse',
                'stock': f'{stock_code},{org_id}',
                'searchkey': '',
                'secid': '',
                'plate': 'sz',
                'category': 'category_ndbg_szsh',
                'trade': '',
                'seDate': f'{start_date}~{end_date}',
                'sortName': '',
                'sortType': '',
                'isHLtitle': 'true'
            }
            
            # 沪市查询
            sse_query = szse_query.copy()
            sse_query.update({
                'column': 'sse',
                'plate': 'sh'
            })
            
            announcements = []
            
            # 查询深市
            try:
                print(f"  🔍 查询深市数据...")
                response = requests.post(self.query_url, headers=self.headers,
                                       data=szse_query, timeout=10)
                if response.status_code == 200:
                    result = response.json()
                    print(f"  📊 深市响应: {type(result)}")
                    szse_announcements = result.get('announcements', []) if result else []
                    if szse_announcements and isinstance(szse_announcements, list):
                        announcements.extend(szse_announcements)
                        print(f"  ✅ 深市找到 {len(szse_announcements)} 条公告")
                    else:
                        print(f"  ⚠️ 深市没有找到公告数据")
                else:
                    print(f"  ❌ 深市查询失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"深市查询失败: {e}")

            # 查询沪市
            try:
                print(f"  🔍 查询沪市数据...")
                response = requests.post(self.query_url, headers=self.headers,
                                       data=sse_query, timeout=10)
                if response.status_code == 200:
                    result = response.json()
                    print(f"  📊 沪市响应: {type(result)}")
                    sse_announcements = result.get('announcements', []) if result else []
                    if sse_announcements and isinstance(sse_announcements, list):
                        announcements.extend(sse_announcements)
                        print(f"  ✅ 沪市找到 {len(sse_announcements)} 条公告")
                    else:
                        print(f"  ⚠️ 沪市没有找到公告数据")
                else:
                    print(f"  ❌ 沪市查询失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"沪市查询失败: {e}")
            
            # 过滤公告
            filtered_announcements = []
            for announcement in announcements:
                title = announcement.get('announcementTitle', '')
                if (search_keyword in title and 
                    '摘要' not in title and 
                    '确认意见' not in title and
                    '招股书' not in title):
                    filtered_announcements.append(announcement)
            
            return filtered_announcements
            
        except Exception as e:
            print(f"搜索公告失败: {e}")
            return []
    
    def download_pdf(self, announcement: Dict, save_dir: str = "pdf") -> Optional[str]:
        """下载PDF文件"""
        try:
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            adjunct_url = announcement.get('adjunctUrl', '')
            if not adjunct_url:
                print("❌ 没有找到PDF下载链接")
                return None

            download_url = self.download_base_url + adjunct_url
            print(f"📥 下载URL: {download_url}")

            # 构造文件名
            stock_code = announcement.get('secCode', '')
            company_name = announcement.get('secName', '')
            title = announcement.get('announcementTitle', '')

            # 清理文件名中的特殊字符
            safe_title = re.sub(r'[<>:"/\\|?*]', '_', title)
            file_name = f"{stock_code}_{company_name}_{safe_title}.pdf"
            file_path = os.path.join(save_dir, file_name)

            # 检查文件是否已存在
            if os.path.exists(file_path):
                print(f"📄 文件已存在: {file_name}")
                return file_path

            # 为下载PDF设置专门的请求头
            download_headers = {
                'User-Agent': random.choice(self.user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Referer': 'http://www.cninfo.com.cn/'
            }

            # 下载文件，增加重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    print(f"📥 尝试下载 (第{attempt + 1}次): {file_name}")
                    response = requests.get(download_url, headers=download_headers,
                                          timeout=30, allow_redirects=True)

                    if response.status_code == 200:
                        # 检查响应内容是否为PDF
                        content_type = response.headers.get('content-type', '').lower()
                        if 'pdf' in content_type or response.content.startswith(b'%PDF'):
                            with open(file_path, 'wb') as f:
                                f.write(response.content)
                            print(f"✅ 下载成功: {file_name}")
                            return file_path
                        else:
                            print(f"⚠️ 响应不是PDF文件，Content-Type: {content_type}")
                    else:
                        print(f"❌ 下载失败，状态码: {response.status_code}")
                        if response.status_code == 404:
                            print(f"🔍 URL可能无效: {download_url}")

                except requests.exceptions.RequestException as e:
                    print(f"❌ 网络请求失败 (第{attempt + 1}次): {e}")
                    if attempt < max_retries - 1:
                        time.sleep(2)  # 等待2秒后重试

            return None

        except Exception as e:
            print(f"下载PDF失败: {e}")
            return None
    
    def convert_pdf_to_txt(self, pdf_path: str, txt_dir: str = "txt") -> Optional[str]:
        """将PDF转换为TXT"""
        try:
            if not os.path.exists(txt_dir):
                os.makedirs(txt_dir)
            
            # 构造TXT文件路径
            pdf_name = os.path.basename(pdf_path)
            txt_name = pdf_name.replace('.pdf', '.txt')
            txt_path = os.path.join(txt_dir, txt_name)
            
            # 检查TXT文件是否已存在
            if os.path.exists(txt_path):
                return txt_path
            
            # 提取文本
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            
            if text:
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                return txt_path
            else:
                print(f"无法从PDF提取文本: {pdf_path}")
                return None
                
        except Exception as e:
            print(f"PDF转换失败: {e}")
            return None
    
    def analyze_keywords(self, txt_content: str, keywords: List[str]) -> Dict[str, int]:
        """分析关键词 - 支持中英文混合搜索和多关键词组合（&符号拼接）"""
        keyword_stats = {}

        for keyword in keywords:
            # 检查是否为多关键词组合
            if '&' in keyword:
                # 拆分&符号连接的关键词
                sub_keywords = [kw.strip() for kw in keyword.split('&') if kw.strip()]
                if len(sub_keywords) > 1:
                    # 多关键词：找到所有子关键词都出现的位置
                    count = self._count_multi_keyword_occurrences(txt_content, sub_keywords)
                else:
                    # 只有一个有效关键词，按单关键词处理
                    count = self._count_single_keyword(txt_content, sub_keywords[0] if sub_keywords else keyword)
            else:
                # 单关键词
                count = self._count_single_keyword(txt_content, keyword)

            keyword_stats[keyword] = count

        return keyword_stats

    def _count_single_keyword(self, txt_content: str, keyword: str) -> int:
        """统计单个关键词的出现次数"""
        # 判断关键词类型
        is_chinese_keyword = bool(re.search(r'[\u4e00-\u9fa5]', keyword))
        is_english_keyword = bool(re.search(r'[a-zA-Z]', keyword))

        if is_english_keyword and not is_chinese_keyword:
            # 纯英文关键词：直接在原文中搜索（不区分大小写）
            keyword_lower = keyword.lower()
            text_lower = txt_content.lower()
            return text_lower.count(keyword_lower)
        elif is_chinese_keyword and not is_english_keyword:
            # 纯中文关键词：使用原来的清理方式
            clean_content = re.sub(r'[^\u4e00-\u9fa5]', '', txt_content)
            return clean_content.count(keyword)
        else:
            # 中英文混合关键词：在原文中直接搜索
            return txt_content.count(keyword)

    def _count_multi_keyword_occurrences(self, txt_content: str, sub_keywords: List[str]) -> int:
        """统计多关键词组合的出现次数 - 确保所有关键词在同一上下文中出现"""
        import re

        # 为每个子关键词找到所有位置
        all_positions = {}
        for sub_keyword in sub_keywords:
            positions = []

            # 判断关键词类型
            is_chinese_keyword = bool(re.search(r'[\u4e00-\u9fa5]', sub_keyword))
            is_english_keyword = bool(re.search(r'[a-zA-Z]', sub_keyword))

            start = 0
            if is_english_keyword and not is_chinese_keyword:
                # 英文关键词：不区分大小写搜索
                keyword_lower = sub_keyword.lower()
                text_lower = txt_content.lower()
                while True:
                    pos = text_lower.find(keyword_lower, start)
                    if pos == -1:
                        break
                    positions.append(pos)
                    start = pos + 1
            elif is_chinese_keyword and not is_english_keyword:
                # 中文关键词：在清理后的文本中搜索，但记录原文位置
                clean_text = re.sub(r'[^\u4e00-\u9fa5]', '', txt_content)
                clean_to_original_map = []
                for i, char in enumerate(txt_content):
                    if re.match(r'[\u4e00-\u9fa5]', char):
                        clean_to_original_map.append(i)

                clean_start = 0
                while True:
                    clean_pos = clean_text.find(sub_keyword, clean_start)
                    if clean_pos == -1:
                        break
                    # 转换为原文位置
                    if clean_pos < len(clean_to_original_map):
                        original_pos = clean_to_original_map[clean_pos]
                        positions.append(original_pos)
                    clean_start = clean_pos + 1
            else:
                # 混合关键词：直接搜索
                while True:
                    pos = txt_content.find(sub_keyword, start)
                    if pos == -1:
                        break
                    positions.append(pos)
                    start = pos + 1

            all_positions[sub_keyword] = positions

        # 如果任何一个子关键词没有找到，则组合关键词出现次数为0
        if any(len(positions) == 0 for positions in all_positions.values()):
            return 0

        # 计算在同一上下文窗口中出现的组合次数
        context_window = 300  # 上下文窗口大小
        combination_count = 0

        # 以第一个关键词的位置为锚点
        first_keyword = sub_keywords[0]
        first_positions = all_positions[first_keyword]

        for anchor_pos in first_positions:
            # 定义搜索窗口
            window_start = max(0, anchor_pos - context_window // 2)
            window_end = min(len(txt_content), anchor_pos + context_window // 2)

            # 检查其他所有关键词是否都在这个窗口内
            all_in_window = True
            for other_keyword in sub_keywords[1:]:
                other_positions = all_positions[other_keyword]
                positions_in_window = [pos for pos in other_positions if window_start <= pos <= window_end]
                if not positions_in_window:
                    all_in_window = False
                    break

            if all_in_window:
                combination_count += 1

        return combination_count

    def analyze_related_parties(self, text: str, related_parties: List[str], innovation_keywords: List[str]) -> Dict:
        """分析关联方与协同创新的关系 - 支持中英文混合"""
        import re

        original_text = text
        analysis_results = {}

        for party in related_parties:
            party_results = {
                'party_name': party,
                'found_positions': [],
                'innovation_contexts': [],
                'has_innovation': False,
                'innovation_keywords_found': []
            }

            # 判断关联方类型并查找位置
            is_chinese_party = bool(re.search(r'[\u4e00-\u9fa5]', party))
            is_english_party = bool(re.search(r'[a-zA-Z]', party))

            if is_english_party and not is_chinese_party:
                # 英文关联方：直接在原文中搜索
                party_positions = self.find_text_positions(original_text, party, case_sensitive=False)
            elif is_chinese_party and not is_english_party:
                # 中文关联方：在清理后的文本中搜索
                clean_text = re.sub(r'[^\u4e00-\u9fa5]', '', text)
                party_positions = self.find_text_positions(clean_text, party)
            else:
                # 混合关联方：直接在原文中搜索
                party_positions = self.find_text_positions(original_text, party)

            if party_positions:
                print(f"  👥 找到关联方'{party}' {len(party_positions)} 次")
                party_results['found_positions'] = party_positions

                # 分析每个位置周围的上下文
                for pos in party_positions:
                    # 判断是否需要位置映射
                    is_clean_pos = is_chinese_party and not is_english_party
                    context = self.extract_context_around_position(
                        original_text, pos, party, context_length=200, is_clean_pos=is_clean_pos
                    )

                    if context:
                        # 检查上下文中是否包含协同创新关键词
                        innovation_found = self.check_innovation_in_context(
                            context, innovation_keywords
                        )

                        if innovation_found['has_innovation']:
                            party_results['has_innovation'] = True
                            party_results['innovation_contexts'].append({
                                'context': context,
                                'innovation_keywords': innovation_found['keywords_found'],
                                'position': pos
                            })

                            # 收集所有找到的创新关键词
                            for kw in innovation_found['keywords_found']:
                                if kw not in party_results['innovation_keywords_found']:
                                    party_results['innovation_keywords_found'].append(kw)

                if party_results['has_innovation']:
                    print(f"    ✅ 发现与'{party}'的协同创新关系，涉及关键词: {party_results['innovation_keywords_found']}")
                else:
                    print(f"    ❌ 未发现与'{party}'的协同创新关系")
            else:
                print(f"  👥 未找到关联方'{party}'")
                # 设置未找到的状态
                party_results['found_positions'] = []
                party_results['innovation_contexts'] = []
                party_results['has_innovation'] = False
                party_results['innovation_keywords_found'] = []
                party_results['not_found'] = True  # 标记为未找到

            # 无论是否找到都添加到结果中
            analysis_results[party] = party_results

        return analysis_results

    def find_text_positions(self, text: str, target: str, case_sensitive: bool = True) -> List[int]:
        """在文本中查找目标文本的所有位置"""
        positions = []
        start = 0

        if case_sensitive:
            search_text = text
            search_target = target
        else:
            search_text = text.lower()
            search_target = target.lower()

        while True:
            pos = search_text.find(search_target, start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + 1
        return positions

    def extract_context_around_position(self, original_text: str, pos: int, target: str, context_length: int = 200, is_clean_pos: bool = True) -> str:
        """提取指定位置周围的上下文"""
        import re

        if is_clean_pos:
            # 如果是清理后文本的位置，需要映射到原始文本
            clean_to_original_map = []
            for i, char in enumerate(original_text):
                if re.match(r'[\u4e00-\u9fa5]', char):
                    clean_to_original_map.append(i)

            if pos >= len(clean_to_original_map):
                return ""

            # 找到原始文本中的位置
            original_pos = clean_to_original_map[pos]
        else:
            # 如果已经是原始文本的位置，直接使用
            original_pos = pos

        # 提取上下文
        start_pos = max(0, original_pos - context_length)
        end_pos = min(len(original_text), original_pos + len(target) + context_length)

        context = original_text[start_pos:end_pos]

        # 清理上下文中的多余空白
        context = re.sub(r'\s+', ' ', context).strip()

        return context

    def check_innovation_in_context(self, context: str, innovation_keywords: List[str]) -> Dict:
        """检查上下文中是否包含协同创新关键词 - 支持中英文混合和多关键词组合"""
        import re

        found_keywords = []

        for keyword in innovation_keywords:
            # 检查是否为多关键词组合
            if '&' in keyword:
                # 拆分&符号连接的关键词
                sub_keywords = [kw.strip() for kw in keyword.split('&') if kw.strip()]
                if len(sub_keywords) > 1:
                    # 检查所有子关键词是否都在上下文中
                    all_found = True
                    for sub_keyword in sub_keywords:
                        if not self._check_single_keyword_in_context(context, sub_keyword):
                            all_found = False
                            break
                    if all_found:
                        found_keywords.append(keyword)
                else:
                    # 只有一个有效关键词，按单关键词处理
                    if sub_keywords and self._check_single_keyword_in_context(context, sub_keywords[0]):
                        found_keywords.append(keyword)
            else:
                # 单关键词
                if self._check_single_keyword_in_context(context, keyword):
                    found_keywords.append(keyword)

        return {
            'has_innovation': len(found_keywords) > 0,
            'keywords_found': found_keywords
        }

    def _check_single_keyword_in_context(self, context: str, keyword: str) -> bool:
        """检查单个关键词是否在上下文中"""
        import re

        # 判断关键词类型
        is_chinese_keyword = bool(re.search(r'[\u4e00-\u9fa5]', keyword))
        is_english_keyword = bool(re.search(r'[a-zA-Z]', keyword))

        if is_english_keyword and not is_chinese_keyword:
            # 英文关键词：不区分大小写搜索
            return keyword.lower() in context.lower()
        elif is_chinese_keyword and not is_english_keyword:
            # 中文关键词：在清理后的上下文中搜索
            clean_context = re.sub(r'[^\u4e00-\u9fa5]', '', context)
            return keyword in clean_context
        else:
            # 混合关键词：直接搜索
            return keyword in context
    
    def crawl_and_analyze(self, stock_codes: List[str], keywords: List[str],
                         search_keyword: str = "年度报告",
                         start_date: str = "2024-01-01",
                         end_date: str = "2025-12-31",
                         use_online: bool = True,
                         task_id: str = None,
                         related_parties: List[str] = None,
                         innovation_keywords: List[str] = None,
                         progress_callback=None) -> Dict:
        """爬取和分析主函数"""
        self.is_running = True
        results = {
            'success': True,
            'message': '',
            'downloaded_files': [],
            'analysis_results': {},
            'related_party_analysis': {},
            'errors': []
        }

        # 使用统一的analysis_id
        analysis_id = task_id if task_id else f"analysis_{int(time.time())}"
        print(f"🆔 使用分析ID: {analysis_id}")
        
        total_steps = len(stock_codes)
        current_step = 0
        
        try:
            for stock_code in stock_codes:
                if not self.is_running:
                    break
                
                current_step += 1
                if progress_callback:
                    progress_callback(current_step, total_steps, f"处理股票: {stock_code}")
                
                # 获取公司信息
                if use_online:
                    stock_info = self.get_orgid_by_code(stock_code)
                    if not stock_info:
                        error_msg = f"无法获取股票 {stock_code} 的信息"
                        results['errors'].append(error_msg)
                        continue
                    
                    company_name = stock_info['zwjc']
                    org_id = stock_info['orgId']
                    
                    # 添加公司信息到数据库
                    self.db.add_company(stock_code, company_name, org_id)
                    
                    # 搜索公告
                    announcements = self.search_announcements(
                        stock_code, org_id, search_keyword, start_date, end_date
                    )
                    
                    if not announcements:
                        error_msg = f"股票 {stock_code} 没有找到相关公告"
                        results['errors'].append(error_msg)
                        continue
                    
                    # 下载和转换
                    for announcement in announcements:
                        if not self.is_running:
                            break
                        
                        # 下载PDF
                        print(f"  📥 开始下载PDF: {announcement.get('announcementTitle', 'Unknown')}")
                        pdf_path = self.download_pdf(announcement)
                        if not pdf_path:
                            print(f"  ❌ PDF下载失败，跳过此公告")
                            continue
                        
                        results['downloaded_files'].append(pdf_path)
                        
                        # 转换为TXT
                        txt_path = self.convert_pdf_to_txt(pdf_path)
                        if not txt_path:
                            continue
                        
                        # 读取TXT内容
                        with open(txt_path, 'r', encoding='utf-8') as f:
                            txt_content = f.read()
                        
                        # 添加年报到数据库
                        report_title = announcement.get('announcementTitle', '')
                        file_name = os.path.basename(txt_path)
                        report_id = self.db.add_report(
                            stock_code=stock_code,
                            company_name=company_name,
                            report_title=report_title,
                            file_name=file_name,
                            file_path=txt_path,
                            txt_content=txt_content
                        )
                        
                        # 关键词分析
                        if keywords and report_id:
                            keyword_stats = self.analyze_keywords(txt_content, keywords)

                            # 保存分析结果（使用统一的analysis_id）
                            self.db.save_keyword_analysis(analysis_id, stock_code, report_id, keyword_stats)

                            if stock_code not in results['analysis_results']:
                                results['analysis_results'][stock_code] = {}
                            results['analysis_results'][stock_code][file_name] = keyword_stats

                            # 关联方分析
                            if related_parties and keywords:
                                related_analysis = self.analyze_related_parties(
                                    txt_content, related_parties, keywords
                                )
                                if related_analysis:
                                    if stock_code not in results['related_party_analysis']:
                                        results['related_party_analysis'][stock_code] = {}
                                    results['related_party_analysis'][stock_code][file_name] = related_analysis
                
                else:
                    # 使用本地数据库
                    reports = self.db.get_reports_by_stock_codes([stock_code])
                    if not reports:
                        error_msg = f"本地数据库中没有找到股票 {stock_code} 的年报"
                        results['errors'].append(error_msg)
                        continue
                    
                    # 分析本地年报
                    for report in reports:
                        if not self.is_running:
                            break
                        
                        if keywords and report['txt_content']:
                            keyword_stats = self.analyze_keywords(report['txt_content'], keywords)

                            # 保存分析结果（使用统一的analysis_id）
                            self.db.save_keyword_analysis(analysis_id, stock_code, report['id'], keyword_stats)

                            if stock_code not in results['analysis_results']:
                                results['analysis_results'][stock_code] = {}
                            results['analysis_results'][stock_code][report['file_name']] = keyword_stats

                            # 关联方分析
                            if related_parties and keywords:
                                related_analysis = self.analyze_related_parties(
                                    report['txt_content'], related_parties, keywords
                                )
                                if related_analysis:
                                    if stock_code not in results['related_party_analysis']:
                                        results['related_party_analysis'][stock_code] = {}
                                    results['related_party_analysis'][stock_code][report['file_name']] = related_analysis
                
                # 添加随机延时
                if use_online:
                    time.sleep(random.uniform(1, 3))
            
            results['message'] = f"处理完成，共处理 {current_step} 个股票"
            
        except Exception as e:
            results['success'] = False
            results['message'] = f"处理过程中出现错误: {str(e)}"
            results['errors'].append(str(e))
        
        finally:
            self.is_running = False
        
        return results
    
    def stop_crawling(self):
        """停止爬取"""
        self.is_running = False
