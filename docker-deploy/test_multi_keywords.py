#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多关键词功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import parse_multi_keywords, check_multi_keyword_match, count_multi_keyword_occurrences, highlight_keyword_in_text

def test_parse_multi_keywords():
    """测试关键词解析功能"""
    print("=== 测试关键词解析功能 ===")
    
    test_cases = [
        ("协同创新", ["协同创新"]),
        ("协同&创新", ["协同", "创新"]),
        ("技术&合作&发展", ["技术", "合作", "发展"]),
        ("产学研", ["产学研"]),
        ("产学&研", ["产学", "研"]),
        ("", [""]),
        ("&", []),
        ("技术&", ["技术"]),
        ("&创新", ["创新"]),
    ]
    
    for keyword, expected in test_cases:
        result = parse_multi_keywords(keyword)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{keyword}' -> {result} (期望: {expected})")

def test_check_multi_keyword_match():
    """测试多关键词匹配功能"""
    print("\n=== 测试多关键词匹配功能 ===")
    
    text = "公司与高校开展协同创新项目，通过技术合作实现共同发展。产学研结合是重要的创新模式。"
    
    test_cases = [
        ("协同创新", True),
        ("协同&创新", True),
        ("技术&合作", True),
        ("产学&研", True),
        ("协同&发展", True),
        ("技术&研发", False),  # 研发不在文本中
        ("创新&模式", True),
        ("高校&企业", False),  # 企业不在文本中
    ]
    
    for keyword, expected in test_cases:
        result = check_multi_keyword_match(text, keyword)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{keyword}' 在文本中匹配: {result} (期望: {expected})")

def test_count_multi_keyword_occurrences():
    """测试多关键词计数功能"""
    print("\n=== 测试多关键词计数功能 ===")
    
    text = "协同创新是重要的。公司通过协同创新实现发展。技术合作也很重要，技术合作能促进创新。"
    
    test_cases = [
        ("协同创新", 2),
        ("协同&创新", 2),  # 协同出现2次，创新出现3次，取最小值2
        ("技术&合作", 2),
        ("技术&发展", 1),  # 技术出现2次，发展出现1次，取最小值1
        ("重要&很", 0),   # 很不在文本中
    ]
    
    for keyword, expected in test_cases:
        result = count_multi_keyword_occurrences(text, keyword)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{keyword}' 计数: {result} (期望: {expected})")

def test_highlight_keyword_in_text():
    """测试关键词高亮功能"""
    print("\n=== 测试关键词高亮功能 ===")
    
    text = "公司与高校开展协同创新项目，通过技术合作实现发展。"
    
    test_cases = [
        "协同创新",
        "协同&创新", 
        "技术&合作",
        "高校&项目",
    ]
    
    for keyword in test_cases:
        result = highlight_keyword_in_text(text, keyword)
        has_highlight = '<mark class="bg-warning">' in result
        status = "✅" if has_highlight else "❌"
        print(f"{status} '{keyword}' 高亮结果包含标记: {has_highlight}")
        if has_highlight:
            print(f"    结果: {result}")

if __name__ == "__main__":
    test_parse_multi_keywords()
    test_check_multi_keyword_match()
    test_count_multi_keyword_occurrences()
    test_highlight_keyword_in_text()
    print("\n=== 测试完成 ===")
